<script setup>

</script>

<template>
  <div class="chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="back-btn">
        <i class="bi bi-arrow-left"></i>
      </div>
      <div class="chat-title">
        <h4>聊天室</h4>
      </div>
      <div class="chat-menu">
        <i class="bi bi-three-dots-vertical"></i>
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" id="chatMessages">
      <!-- 示例消息 -->
      <div class="message received">
        <div class="message-content">
          <div class="message-text">你好！最近怎么样？</div>
          <div class="message-time">10:30 AM</div>
        </div>
      </div>

      <div class="message sent">
        <div class="message-content">
          <div class="message-text">我很好，谢谢！你呢？</div>
          <div class="message-time">10:32 AM</div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-area">
      <div class="input-addons">
        <button class="btn-addon">
          <i class="bi bi-emoji-smile"></i>
        </button>
        <button class="btn-addon">
          <i class="bi bi-paperclip"></i>
        </button>
      </div>
      <div class="message-input">
        <input type="text" id="messageInput" placeholder="输入消息...">
      </div>
      <button class="btn-send" id="sendButton">
        <i class="bi bi-send"></i>
      </button>
    </div>
  </div>
</template>
<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

body {
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  /* padding: 10px; */
}

/* 聊天容器 */
.chat-container {
  width: 100%;
  max-width: 500px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background-color: #007bff;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-btn, .chat-menu {
  font-size: 20px;
  cursor: pointer;
}

.chat-title h4 {
  margin: 0;
  font-size: 18px;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
}

.message {
  margin-bottom: 15px;
  max-width: 80%;
  display: flex;
}

.message.received {
  align-self: flex-start;
}

.message.sent {
  align-self: flex-end;
}

.message-content {
  display: flex;
  flex-direction: column;
}

.message-text {
  padding: 10px 15px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.received .message-text {
  background-color: white;
  border-top-left-radius: 4px;
}

.message.sent .message-text {
  background-color: #007bff;
  color: white;
  border-top-right-radius: 4px;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  align-self: flex-end;
}

/* 输入区域 */
.chat-input-area {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: white;
  border-top: 1px solid #eee;
}

.input-addons {
  display: flex;
  gap: 10px;
  margin-right: 10px;
}

.btn-addon {
  background: none;
  border: none;
  color: #666;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
}

.message-input {
  flex: 1;
}

.message-input input {
  width: 100%;
  padding: 10px 15px;
  border-radius: 20px;
  border: 1px solid #eee;
  outline: none;
  font-size: 15px;
}

.btn-send {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-left: 10px;
}
</style>