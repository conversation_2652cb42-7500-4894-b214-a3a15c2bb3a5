import http from './http';

interface Setting {
  id: number;
  title: string;
  background: string;
  avatar: string;
  type: number;
  createTime: string;
  updateTime: string;
}

interface ApiResponse {
  msg: string;
  code: number;
  data: {
    records: Setting[];
    total: number;
  };
}

/**
 * 获取系统设置（取第一个设置项）
 */
export const getSystemSettings = async (): Promise<Setting | null> => {
  try {
    const res: ApiResponse = await http.get('/voiceSetting/listSetting'); 
    if (res.code === 200 && res.data.records.length > 0) {
      return res.data.records[0];
    }
    return null;
  } catch (error) {
    console.error('获取系统设置失败:', error);
    return null;
  }
};
