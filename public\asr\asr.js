!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).asr={})}(this,function(t){"use strict";a=Math,h=(g={}).lib={},e=h.Base={extend:function(t){y.prototype=this;var e=new y;return t&&e.mixIn(t),e.hasOwnProperty("init")||(e.init=function(){e.$super.init.apply(this,arguments)}),(e.init.prototype=e).$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},c=h.WordArray=e.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||i).stringify(this)},concat:function(t){var e=this.words,i=t.words,n=this.sigBytes;if(t=t.sigBytes,this.clamp(),n%4)for(var s=0;s<t;s++)e[n+s>>>2]|=(i[s>>>2]>>>24-s%4*8&255)<<24-(n+s)%4*8;else if(65535<i.length)for(s=0;s<t;s+=4)e[n+s>>>2]=i[s>>>2];else e.push.apply(e,i);return this.sigBytes+=t,this},clamp:function(){var t=this.words,e=this.sigBytes;t[e>>>2]&=4294967295<<32-e%4*8,t.length=a.ceil(e/4)},clone:function(){var t=e.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],i=0;i<t;i+=4)e.push(4294967296*a.random()|0);return new c.init(e,t)}}),f=g.enc={},i=f.Hex={stringify:function(t){var e=t.words;t=t.sigBytes;for(var i=[],n=0;n<t;n++){var s=e[n>>>2]>>>24-n%4*8&255;i.push((s>>>4).toString(16)),i.push((15&s).toString(16))}return i.join("")},parse:function(t){for(var e=t.length,i=[],n=0;n<e;n+=2)i[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new c.init(i,e/2)}},n=f.Latin1={stringify:function(t){var e=t.words;t=t.sigBytes;for(var i=[],n=0;n<t;n++)i.push(String.fromCharCode(e[n>>>2]>>>24-n%4*8&255));return i.join("")},parse:function(t){for(var e=t.length,i=[],n=0;n<e;n++)i[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new c.init(i,e)}},s=f.Utf8={stringify:function(t){try{return decodeURIComponent(escape(n.stringify(t)))}catch(t){throw Error("Malformed UTF-8 data")}},parse:function(t){return n.parse(unescape(encodeURIComponent(t)))}},o=h.BufferedBlockAlgorithm=e.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=s.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(t){var e=this._data,i=e.words,n=e.sigBytes,s=this.blockSize,o=n/(4*s),o=t?a.ceil(o):a.max((0|o)-this._minBufferSize,0),n=a.min(4*(t=o*s),n);if(t){for(var r=0;r<t;r+=s)this._doProcessBlock(i,r);r=i.splice(0,t),e.sigBytes-=n}return new c.init(r,n)},clone:function(){var t=e.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),h.Hasher=o.extend({cfg:e.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){o.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(i){return function(t,e){return new i.init(e).finalize(t)}},_createHmacHelper:function(i){return function(t,e){return new r.HMAC.init(i,e).finalize(t)}}}),r=g.algo={};var a,e,c,i,n,s,o,r,h,u,d,l,g,p,f=g;function y(){}u=(g=(h=f).lib).WordArray,d=g.Hasher,l=[],g=h.algo.SHA1=d.extend({_doReset:function(){this._hash=new u.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var i,n=this._hash.words,s=n[0],o=n[1],r=n[2],a=n[3],c=n[4],h=0;h<80;h++)l[h]=h<16?0|t[e+h]:(i=l[h-3]^l[h-8]^l[h-14]^l[h-16])<<1|i>>>31,i=(s<<5|s>>>27)+c+l[h],i=h<20?i+(1518500249+(o&r|~o&a)):h<40?i+(1859775393+(o^r^a)):h<60?i+((o&r|o&a|r&a)-1894007588):i+((o^r^a)-899497514),c=a,a=r,r=o<<30|o>>>2,o=s,s=i;n[0]=n[0]+s|0,n[1]=n[1]+o|0,n[2]=n[2]+r|0,n[3]=n[3]+a|0,n[4]=n[4]+c|0},_doFinalize:function(){var t=this._data,e=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[14+(64+n>>>9<<4)]=Math.floor(i/4294967296),e[15+(64+n>>>9<<4)]=i,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=d.clone.call(this);return t._hash=this._hash.clone(),t}}),h.SHA1=d._createHelper(g),h.HmacSHA1=d._createHmacHelper(g),p=f.enc.Utf8,f.algo.HMAC=f.lib.Base.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=p.parse(e));var i=t.blockSize,n=4*i;(e=e.sigBytes>n?t.finalize(e):e).clamp();for(var t=this._oKey=e.clone(),e=this._iKey=e.clone(),s=t.words,o=e.words,r=0;r<i;r++)s[r]^=1549556828,o[r]^=909522486;t.sigBytes=e.sigBytes=n,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher;return t=e.finalize(t),e.reset(),e.finalize(this._oKey.clone().concat(t))}}),window&&(window.CryptoJSTest=f);const x=["appid","secretkey","signCallback","echoCancellation"];const w=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)});async function S(t,e){if(!e.appid||!e.secretid)return t.isLog&&console.log(t.requestId,"请确认是否填入账号信息",m),t.OnError("请确认是否填入账号信息"),!1;t=function(t,i){let n="",e="asr.cloud.tencent.com/asr/v2/";t.appid&&(e+=t.appid);var s=Object.keys(i);s.sort();for(let t=0,e=s.length;t<e;t++)n+=`&${s[t]}=`+i[s[t]];return e+"?"+n.slice(1)}(e,await async function(t){let e={};var i=(new Date).getTime(),n=await new Promise((t,e)=>{try{const i=new XMLHttpRequest;i.open("GET","https://asr.cloud.tencent.com/server_time",!0),i.send(),i.onreadystatechange=function(){4===i.readyState&&200===i.status&&t(i.responseText)}}catch(t){e(t)}}),s=(e.secretid=t.secretid||"",e.engine_model_type=t.engine_model_type||"16k_zh",e.timestamp=parseInt(n)||Math.round(i/1e3),e.expired=Math.round(i/1e3)+86400,e.nonce=Math.round(i/1e5),e.voice_id=w(),e.voice_format=t.voice_format||1,{...t});for(let t=0,e=x.length;t<e;t++)s.hasOwnProperty(x[t])&&delete s[x[t]];return e={...s,...e}}(e));let i="";return i=e.signCallback?e.signCallback(t):function(t,e){e=window.CryptoJSTest.HmacSHA1(e,t),t=function(e){let i="";for(let t=0;t<e.length;t++)i+=String.fromCharCode(e[t]);return i}(function(t){var e=t.words,i=t.sigBytes,n=new Uint8Array(i);for(let t=0;t<i;t++)n[t]=e[t>>>2]>>>24-t%4*8&255;return n}(e));return window.btoa(t)}(e.secretkey,t),`wss://${t}&signature=`+encodeURIComponent(i)}const m="SpeechRecognizer";class k{constructor(t,e,i){this.socket=null,this.isSignSuccess=!1,this.isSentenceBegin=!1,this.query={...t},this.isRecognizeComplete=!1,this.requestId=e,this.isLog=i,this.sendCount=0,this.getMessageList=[]}stop(){this.socket&&1===this.socket.readyState?(this.socket.send(JSON.stringify({type:"end"})),this.isRecognizeComplete=!0):this.socket&&1===this.socket.readyState&&this.socket.close()}async start(){this.socket=null,this.getMessageList=[];var t=await S(this,this.query);if(t){if(this.isLog&&console.log(this.requestId,"get ws url",t,m),"WebSocket"in window)this.socket=new WebSocket(t);else{if(!("MozWebSocket"in window))return this.isLog&&console.log(this.requestId,"浏览器不支持WebSocket",m),void this.OnError("浏览器不支持WebSocket");this.socket=new MozWebSocket(t)}this.socket.onopen=t=>{this.isLog&&console.log(this.requestId,"连接建立",t,m)},this.socket.onmessage=async t=>{try{this.getMessageList.push(JSON.stringify(t));var e=JSON.parse(t.data);0!==e.code?(1===this.socket.readyState&&this.socket.close(),this.isLog&&console.log(this.requestId,JSON.stringify(e),m),this.OnError(e)):(this.isSignSuccess||(this.OnRecognitionStart(e),this.isSignSuccess=!0),1===e.final?this.OnRecognitionComplete(e):(e.result&&(0===e.result.slice_type?(this.OnSentenceBegin(e),this.isSentenceBegin=!0):2===e.result.slice_type?(this.isSentenceBegin||this.OnSentenceBegin(e),this.OnSentenceEnd(e)):this.OnRecognitionResultChange(e)),this.isLog&&console.log(this.requestId,e,m)))}catch(t){this.isLog&&console.log(this.requestId,"socket.onmessage catch error",JSON.stringify(t),m)}},this.socket.onerror=t=>{this.isLog&&console.log(this.requestId,"socket error callback",t,m),this.socket.close(),this.OnError(t)},this.socket.onclose=t=>{try{this.isRecognizeComplete||(this.isLog&&console.log(this.requestId,"socket is close and error",JSON.stringify(t),m),this.OnError(t))}catch(t){this.isLog&&console.log(this.requestId,"socket is onclose catch"+this.sendCount,JSON.stringify(t),m)}}}else this.isLog&&console.log(this.requestId,"鉴权失败",m),this.OnError("鉴权失败")}close(){this.socket&&1===this.socket.readyState&&this.socket.close(1e3)}write(t){try{if(!this.socket||"1"!==String(this.socket.readyState))return setTimeout(()=>{this.socket&&1===this.socket.readyState&&this.socket.send(t)},40),!1;this.sendCount+=1,this.socket.send(t)}catch(t){this.isLog&&console.log(this.requestId,"发送数据 error catch",t,m)}}OnRecognitionStart(t){}OnSentenceBegin(t){}OnRecognitionResultChange(){}OnSentenceEnd(){}OnRecognitionComplete(){}OnError(){}}"undefined"!=typeof window&&(window.SpeechRecognizer=k);const C=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}),O=window.URL.createObjectURL(new Blob([`
class MyProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super(options);
    this.audioData = [];
    this.preTime = 0;
  }

  process(inputs) {
    // 去处理音频数据
    // eslint-disable-next-line no-undef
    if (inputs[0][0]) {
      const output = ${R}(inputs[0][0], sampleRate);
      const audioData = ${v}(output);
      const data = [...new Int8Array(audioData.buffer)];
      this.audioData = this.audioData.concat(data);
      if (new Date().getTime() - this.preTime > 100) {
        this.port.postMessage({
          audioData: new Int8Array(this.audioData)
        });
        this.preTime = new Date().getTime();
        this.audioData = [];
      }
        return true;
      }
  }
}

registerProcessor('my-processor', MyProcessor);
`],{type:"text/javascript"})),_=["appId","secretKey","secretId","audioTrack"];function v(e){var t=2*e.length,t=new ArrayBuffer(t),i=new DataView(t);let n=0;for(let t=0;t<e.length;t++,n+=2){var s=Math.max(-1,Math.min(1,e[t]));i.setInt16(n,s<0?32768*s:32767*s,!0)}return i}function R(t,e=44100){var i=new Float32Array(t),n=Math.round(i.length*(16e3/e)),s=new Float32Array(n),o=(i.length-1)/(n-1);s[0]=i[0];for(let t=1;t<n-1;t++){var r=t*o,a=Math.floor(r).toFixed(),c=Math.ceil(r).toFixed();s[t]=i[a]+(i[c]-i[a])*(r-a)}return s[n-1]=i[i.length-1],s}window&&(window.ASR=class{constructor(t,e){this.audioTrack=t.audioTrack,this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.speechRecognizer=null,this.isCanSendData=!1,this.audioData=[],this.secretkey=t.secretKey,this.params={...t,secretid:t.secretId,appid:t.appId},this.isLog=e,this.OnRecognitionStart=function(){},this.OnSentenceBegin=function(){},this.OnRecognitionResultChange=function(){},this.OnSentenceEnd=function(){},this.OnRecognitionComplete=function(){},this.OnError=function(){},this.OnChange=function(){}}signCallback(t){var e=this.secretkey,t=function(e){let i="";for(let t=0;t<e.length;t++)i+=String.fromCharCode(e[t]);return i}(function(t){var e=t["words"],i=t["sigBytes"],n=new Uint8Array(i);for(let t=0;t<i;t++)n[t]=e[t>>>2]>>>24-t%4*8&255;return n}(window.CryptoJSTest.HmacSHA1(t,e)));return window.btoa(t)}start(){if(!this.speechRecognizer){var i={...this.params};for(let t=0,e=_.length;t<e;t++)i.hasOwnProperty(_[t])&&delete i[_[t]];var t={signCallback:this.signCallback.bind(this),...i};this.speechRecognizer=new k(t,C(),this.isLog)}this.speechRecognizer.OnRecognitionStart=t=>{this.isCanSendData=!0,this.OnRecognitionStart(t)},this.speechRecognizer.OnSentenceBegin=t=>{this.OnSentenceBegin(t),this.OnChange(t)},this.speechRecognizer.OnRecognitionResultChange=t=>{this.OnRecognitionResultChange(t),this.OnChange(t)},this.speechRecognizer.OnSentenceEnd=t=>{this.OnSentenceEnd(t),this.OnChange(t)},this.speechRecognizer.OnRecognitionComplete=t=>{this.OnRecognitionComplete(t)},this.speechRecognizer.OnError=t=>{this.isCanSendData=!1,this.OnError(t)},this.speechRecognizer.start(),this.getAudioData()}getAudioData(){var t=new MediaStream;t.addTrack(this.audioTrack);const e=this.audioContext.createMediaStreamSource(t);this.audioContext.audioWorklet?this.audioContext.audioWorklet.addModule(O).then(()=>{var t=new AudioWorkletNode(this.audioContext,"my-processor",{numberOfInputs:1,numberOfOutputs:1,channelCount:1});t.port.onmessage=t=>{this.isCanSendData&&this.speechRecognizer.write(t.data.audioData)},e.connect(t).connect(this.audioContext.destination)}).catch(console.error):((t=this.audioContext.createScriptProcessor(0,1,1)).onaudioprocess=t=>{var t=v(R(t.inputBuffer.getChannelData(0),this.audioContext.sampleRate));this.audioData.push(...new Int8Array(t.buffer)),100<(new Date).getTime()-this.preTime&&this.isCanSendData&&(t=new Int8Array(this.audioData),this.speechRecognizer.write(t),this.preTime=(new Date).getTime(),this.audioData=[])},e.connect(t),t.connect(this.audioContext.destination))}stop(){this.speechRecognizer.stop(),this.audioContext&&this.audioContext.suspend()}}),t.guid=C,Object.defineProperty(t,"__esModule",{value:!0})});