import axios from 'axios';

// 创建 axios 实例
const http = axios.create({
    baseURL: '/api', // API的基础路径
    timeout: 50000, // 请求超时时间
    headers:{
        'Content-Type':"application/json"
    }
});

// 添加请求拦截器
http.interceptors.request.use(
    config => {
        // 在发送请求之前做些什么，例如设置token
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);

// 添加响应拦截器
http.interceptors.response.use(
    response => {
        // 对响应数据做点什么
        return response.data;
    },
    error => {
        // 对响应错误做点什么
        if (error.response && error.response.status === 401) {
            // 例如，处理401错误（未授权）可以重定向到登录页面等
            console.error('Unauthorized, please login again.');
        }
        return Promise.reject(error);
    }
);

export default http;