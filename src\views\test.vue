<script setup>
import {chatMessage} from '@/http/voiceApi.js'
import {nextTick, ref} from 'vue'
let webAudioSpeechRecognizer;
const voicePath = ref('')
const audioElement = ref('')
const params = {
  signCallback: signCallback, // 鉴权函数，若直接使用默认鉴权函数。可不传此参数
  // 用户参数
  secretid:  config.secretId,
  secretkey: config.secretKey,
  appid: config.appId,
  // 临时密钥参数，非必填
  // token: config.token,
  // 实时识别接口参数
  engine_model_type : '16k_zh', // 因为内置WebRecorder采样16k的数据，所以参数 engineModelType 需要选择16k的引擎，为 '16k_zh'
  // 以下为非必填参数，可跟据业务自行修改
  // voice_format : 1,
  // hotword_id : '08003a00000000000000000000000000',
  // needvad: 1,
  // filter_dirty: 1,
  // filter_modal: 2,
  // filter_punc: 0,
  // convert_num_mode : 1,
  // word_info: 2
}
const resultText = ref('')
const resultState = ref('')
function startListening() {
  webAudioSpeechRecognizer = new WebAudioSpeechRecognizer(params);
  // 开始识别
  webAudioSpeechRecognizer.OnRecognitionStart = (res) => {
    console.log('开始识别', res);
    resultState.value = `开始识别--${JSON.stringify(res)}`
  };
  // 一句话开始
  webAudioSpeechRecognizer.OnSentenceBegin = (res) => {
    console.log('一句话开始', res);
    resultState.value = `一句话开始--${JSON.stringify(res)}`
  };
  let timeout = 0;
  // 识别变化时
  webAudioSpeechRecognizer.OnRecognitionResultChange = (res) => {
    console.log('识别变化时', res);
    resultState.value = `识别变化时--${JSON.stringify(res)}`
    const currentText = `${res.result.voice_text_str}`;
    resultText.value = currentText;
    if (currentText !== '') {
      clearTimeout(timeout);
      timeout = setTimeout(async ()=>{
        stopListening()
        await handleVoice(currentText)
      },1000)
    }
  };
  // 一句话结束
  webAudioSpeechRecognizer.OnSentenceEnd = (res) => {
    console.log('一句话结束', res);
    resultText.value = res.result.voice_text_str;
  };
  // 识别结束
  webAudioSpeechRecognizer.OnRecognitionComplete = (res) => {
    console.log('识别结束', res);
    resultState.value = `OnRecognitionComplete--${JSON.stringify(res)}`
  };
  // 识别错误
  webAudioSpeechRecognizer.OnError = (res) => {
    console.log('识别失败', res)
    resultState.value = `识别失败,${res.result.error}`;
  };
  webAudioSpeechRecognizer.start();

  let audio = audioElement.value
  audio.muted = true;
  voicePath.value = `https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/temp-8c1b0ebb-2d1a-421b-8669-c7a257179d2d.mp3`
  audio.load()
  nextTick(()=>{
    audio.play()
    setTimeout(()=>{
      audio.pause()
    },200)
  })
}
function stopListening() {
  webAudioSpeechRecognizer.stop();
}
let conversation_id = ""
// 处理语音消息
async function handleVoice(text){
  // transcript.value = "请求中..."
  // console.log("Final Transcript: " + transcript); // 输出最终结果
  let res = await chatMessage({
    text,
    conversation_id,
    'isAudio': true,
  })
  console.log("res---",res)
  // conversation_id = res.data.conversation_id
  let answer = res.data.answer
  resultText.value = JSON.stringify(answer)
  playText(answer)
  playVoice(res.data.url)
}
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
function playText(answer){
  (async ()=>{
    let groups = splitIntoGroups(answer,15);
    for (const group of groups) {
      resultText.value = group
      await sleep(3100)
    }
  })()
}
// 分割文字
function splitIntoGroups(str, groupSize) {
  let result = [];
  for (let i = 0; i < str.length; i += groupSize) {
    result.push(str.slice(i, i + groupSize));
  }
  return result;
}
const audioEndHandle = ()=>{
  const audio = audioElement.value
  audio.muted = true
  audio.pause()
  // 开启说话
  startListening()
  // statusText.value = "请说话.."
  resultText.value = "请说话.."
}
const playVoice = (url) => {
  console.log("audioElement--",audioElement)
  // 外部链接
  // voicePath.value = `https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/temp-0f82ada4-7ca3-42d9-9d6f-394df0ae5eb0.mp3`
  try{
    voicePath.value = url
    const audio = audioElement.value
    // 从头开始
    audio.currentTime = 0
    audio.muted = false
    // 重新加载
    audio.load()
    nextTick(()=>{
      // 播放
      audio?.play()
    })
  }catch (e) {

  }
}
</script>

<template>
<div class="test">
  <button class="staring" @click="startListening()">开启对话</button>
  <button class="stop" @click="stopListening()">停止对话</button>
  <div class="test">识别结果:{{resultText}}</div>
  <div class="test">识别状态:{{resultState}}</div>
  <audio :src="voicePath" @ended="audioEndHandle()" ref="audioElement" controls style="display: none"></audio>
</div>
</template>

<style scoped>

</style>