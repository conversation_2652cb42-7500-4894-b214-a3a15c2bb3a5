// 生成1到3之间的随机整数
function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
// 开场白
let startArr = [
    {
        url:'https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/start/1-1749190596932-304296.wav',
        text: '嗨，我在这里，想聊什么都可以。'
    },
    {
        url:'https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/start/2-1749190623030-781674.wav ',
        text: "我一直都在，你随时可以开口。"
    },
    {
        url:'https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/start/3-1749190644824-630954.wav',
        text: '我们可以轻松聊聊，没压力喔。'
    },
]
// 响应
let responseArr = [
    {
        url:'https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/response/rs1-1749190777415-944946.mp3',
        text: '不急，我们慢慢来就好。'
    },
    {
        url:'https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/response/rs2-1749190799252-566646.mp3',
        text: "我在呢，慢慢说都可以。"
    },
    {
        url:'https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/response/rs3-1749190817401-452752.mp3',
        text: '我听得见呢，你可以放轻松一点。'
    },
]
// 开场白
function getStartVoice(){
    // 播放语音
    let r = getRandomInt(0,2)
    let item = startArr[r]
    return item
}
// 响应
function getResponseVoice() {
    let r = getRandomInt(0,2)
    let item = responseArr[r]
    return item
}
// 判断设备
function detectDevice() {
    var userAgent = navigator.userAgent || navigator.vendor || window.opera;
    // iOS detection
    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return "iOS";
    }
    // Android detection
    if (/Android/.test(userAgent)) {
        return "Android";
    }
    // 其他情况
    return "Unknown";
}
// 判断是否是微信浏览器
function isWeChatBrowser() {
    return /MicroMessenger/i.test(navigator.userAgent)
}
export {
    getRandomInt,
    getStartVoice,
    getResponseVoice,
    detectDevice,
    isWeChatBrowser
}