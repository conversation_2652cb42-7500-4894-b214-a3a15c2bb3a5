<script setup>
import {nextTick, onMounted, ref} from "vue";
import {chatMessage} from '@/http/voiceApi.js'
import {audioSpeechStar,audioSpeechStop} from "@/utils/audioUtils.js"
// 状态
const stateBtn = ref('start')
// 状态文本
const state = ref('哈喽~今天还好吗?要不要聊一下?')
let recognition = '';
const transcript = ref("")
const responseContent = ref("")
let conversation_id = ""
// 按钮文字
const btnText = ref("按下说话")
// 监听标志
let flag = false
// 是否请求中
let isResponse = false

const init = async ()=>{
  let SpeechRecognition = ""
  if ('webkitSpeechRecognition' in window) {
    // 使用webkitSpeechRecognition（在旧版Chrome中使用）
    SpeechRecognition = webkitSpeechRecognition;
  } else {
    // 使用标准SpeechRecognition接口
    SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition || null;
  }
  recognition = new SpeechRecognition();
  console.log("recognition==",recognition)
  recognition.lang = 'zh-CN,en'; // 设置语言为英语（美国）
  recognition.continuous = true; // 连续识别
  recognition.interimResults = true; // 显示临时结果（有助于提高准确性）
  recognition.onresult = function(event) {
    for (let i = event.resultIndex; i < event.results.length; ++i) {
      if (event.results[i].isFinal) {
        // 最终的值
        // transcript.value += event.results[i][0].transcript;
        (async ()=>{
          let text = event.results[i][0].transcript
          await handleVoice(text)
        })()
      } else {
        // speechCancel()
        // 展示
        transcript.value = event.results[i][0].transcript; // 输出临时结果，有助于提高识别准确率
      }
    }
  };
}
const stateText = {
  start:{
    text:'哈喽~今天还好吗?要不要聊一下?',
    state: "start"
  },
  send:{
    text:'聆听中',
    state: "send"
  },
  interrupt:{
    text:'思考中... \n (请等待大约3至4秒时间)',
    state: "interrupt"
  },
}
const stateHandler = (state1)=>{
  let start = stateText[state1];
  stateBtn.value = start.state
  state.value = start.text
}
// init()
// 处理语音消息
async function handleVoice(text){
  // transcript.value = "请求中..."
  // console.log("Final Transcript: " + transcript); // 输出最终结果
  if (stateBtn.value !== 'interrupt') return
  transcript.value = ''
  let res = await chatMessage({
    text,
    conversation_id,
    'isAudio': true,
  })
  console.log("res---",res)
  if (stateBtn.value !== 'interrupt') return
  state.value = ''
  // conversation_id = res.data.conversation_id
  let answer = res.data.answer
  responseContent.value = JSON.stringify(answer)
  // 播放语音
  playVoice(res.data.url)
  playText(answer)
}
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
function playText(answer){
  (async ()=>{
    let groups = splitIntoGroups(answer,15);
    for (const group of groups) {
      if (stateBtn.value !== 'interrupt') break
      transcript.value = group
      await sleep(3100)
    }
  })()
}
// 分割文字
function splitIntoGroups(str, groupSize) {
  let result = [];
  for (let i = 0; i < str.length; i += groupSize) {
    result.push(str.slice(i, i + groupSize));
  }
  return result;
}
function speechStart() {
  audioSpeechStar(transcript,async function () {
    console.log("发送网络请求")
    let answer = transcript.value
    isResponse = true
    try {
      // 识别结束 发送网络请求
      await handleVoice(answer)
    }catch (e){

    }
    isResponse = false
  })
}
function startListening() {
  // 播放语音
  stateHandler('send')
  try{
    let audio = audioElement.value
    audio.muted = false;
    voicePath.value = ""
    audio.volume = 1;
    audio.load()
    audio.play()
    nextTick(()=>{
      audio.pause()
      speechStart()
    })
  }catch (e) {

  }
}
onMounted(()=>{
  // dblclick 是 JavaScript 中用于监听双击（double click）事件的事件类型。当用户在同一元素上快速连续点击两次时，会触发 dblclick 事件。
  document.addEventListener('dblclick', function(e) {
    e.preventDefault();
  }, { passive: false });
})

function stopListening() {
  stateHandler("interrupt")
  audioSpeechStop()
}
function playStop(){
  const audio = audioElement.value
  audio.muted = true
  audio.pause()
}
function cancelState(){
  playStop()
  audioSpeechStop()
  transcript.value = ''
  stateHandler('start')
}
const audioElement = ref(null)
const voicePath = ref('')
const playVoice = (url) => {
  console.log("audioElement--",audioElement)
  // 外部链接
  // voicePath.value = `https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/temp-0f82ada4-7ca3-42d9-9d6f-394df0ae5eb0.mp3`
  try{
    voicePath.value = url
    const audio = audioElement.value
    // 从头开始
    audio.currentTime = 0
    audio.muted = false
    // 重新加载
    audio.load()
    nextTick(()=>{
      // 播放
      audio?.play()
    })
  }catch (e) {

  }
}
const audioEndHandle = ()=>{
  if (isResponse){
    transcript.value = "思考中..."
    return
  }
  flag = false
  const audio = audioElement.value
  audio.muted = true
  audio.pause()
  // statusText.value = "请说话.."
  transcript.value = "请说话.."
  speechStart()
  stateHandler('send')
}
// 打断
const interruptClick = ()=>{
  playStop()
  speechStart()
  stateHandler('send')
}
</script>

<template>
  <audio :src="voicePath" @ended="audioEndHandle()" ref="audioElement"  controls style="display: none"></audio>
  <div class="app">
    <div class="light-spot"></div>
    <div class="content">
      <div class="icon">
        <router-link to="/chat">
          <img class="img" src="@/assets/text.png" alt="">
        </router-link>
      </div>
      <div class="text-tip">文字互动</div>

      <div class="text-out-box">
        <div class="state-box" v-show="transcript ==='' ">
          <div>{{state}}</div>
        </div>
        <div class="cartoon" v-show="stateBtn === 'send' ">
          <img src="/images/home/<USER>" alt="">
        </div>
        <div class="text-box">{{transcript}}</div>
      </div>
<!--      <button class="button" @click="clickListening()">-->
<!--        {{btnText}}-->
<!--      </button>-->
      <div class="submit" >
        <div v-show="stateBtn === 'start'">
          <img class="start"  @click="startListening()" :src="`/images/home/<USER>" alt="">
          <div>对话</div>
        </div>
        <div v-show="stateBtn === 'send'">
          <img class="send"  @click="stopListening()" :src="`/images/home/<USER>" alt="">
          <div>发送</div>
        </div>
        <div v-show="stateBtn === 'interrupt'">
          <img class="interrupt"  @click="interruptClick()" src="/images/home/<USER>" alt="">
          <div>打断</div>
        </div>
      </div>
      <div class="cancel">
        <img @click="cancelState()" src="/images/home/<USER>" alt="">
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
body, html,.app {
  //height: calc(100vh - 74px);
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  background: linear-gradient(to bottom, #000066, #0000cc);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.content {
  text-align: center;
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  color: white;
  .text-out-box {
    position: absolute;
    left: 50%;
    width: 80%;
    transform: translateX(-50%);
    bottom: 13rem;
    font-size: 16px;
    .text-box {
      color: white;
    }
  }
  .cartoon{
    img{
      width: 60px;
      height: 60px;
    }
  }
  .state-box{
    white-space: pre-wrap;
    font-size: 16px;
  }
  .submit{
    position: absolute;
    width: 80%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 6rem;
    img{
      width: 70px;
      height: 70px;
    }
  }
  .cancel{
    position: absolute;
    width: 80%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 2rem;
    img{
      width: 80px;
      height: 80px;
    }
  }
}

.icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon .img{
  width: 30px;
  height: 30px;
}
.text-tip{
  position: absolute;
  top: 70px;
  right: 5px;
  color: white;
  width: 80px;
}
.icon-dots {
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='20' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='50' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='80' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='35' cy='50' r='5' fill='%2300008B'/%3E%3Ccircle cx='65' cy='50' r='5' fill='%2300008B'/%3E%3Ccircle cx='50' cy='80' r='5' fill='%2300008B'/%3E%3C/svg%3E");
  //background: url("/public/logo.png") no-repeat center;
  background-size: cover;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
}

.light-spot {
  position: absolute;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(63,255,255,0.9) 0%, rgba(63,255,255,0) 70%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  animation: pulse 2s infinite;
  z-index: 0;
}
.button {
  background: linear-gradient(to right, rgba(135,206,250,0.8), rgba(30,144,255,0.8));
  color: white;
  border: none;
  padding: 12px 40px;
  border-radius: 30px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
  position: absolute;
  width: 80%;
  left: 50%;
  transform: translateX(-50%);
  bottom: 2rem;
}

.button:hover {
  background: linear-gradient(to right, rgba(30,144,255,0.8), rgba(135,206,250,0.8));
}
</style>