<script setup>
import { nextTick, onMounted, ref } from "vue";
import { chatMessage } from "@/http/voiceApi.js";
import { audioSpeechStar, audioSpeechStop } from "@/utils/audioUtils.js";
import { getSystemSettings } from "@/http/settingApi"; //获取头像标题，背景。
// 状态
const stateBtn = ref("start");
// 状态文本
const state = ref("哈喽~今天还好吗?要不要聊一下?");
let recognition = "";
const transcript = ref("");
const responseContent = ref("");
let conversation_id = "";
// 按钮文字
const btnText = ref("按下说话");
// 监听标志
let flag = false;
// 是否请求中
let isResponse = false;

const init = async () => {
  let SpeechRecognition = "";
  if ("webkitSpeechRecognition" in window) {
    // 使用webkitSpeechRecognition（在旧版Chrome中使用）
    SpeechRecognition = webkitSpeechRecognition;
  } else {
    // 使用标准SpeechRecognition接口
    SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition || null;
  }
  recognition = new SpeechRecognition();
  console.log("recognition==", recognition);
  recognition.lang = "zh-CN,en"; // 设置语言为英语（美国）
  recognition.continuous = true; // 连续识别
  recognition.interimResults = true; // 显示临时结果（有助于提高准确性）
  recognition.onresult = function (event) {
    for (let i = event.resultIndex; i < event.results.length; ++i) {
      if (event.results[i].isFinal) {
        // 最终的值
        // transcript.value += event.results[i][0].transcript;
        (async () => {
          let text = event.results[i][0].transcript;
          await handleVoice(text);
        })();
      } else {
        // speechCancel()
        // 展示
        transcript.value = event.results[i][0].transcript; // 输出临时结果，有助于提高识别准确率
      }
    }
  };
};
const stateText = {
  start: {
    text: "哈喽~今天还好吗?要不要聊一下?",
    state: "start",
  },
  send: {
    text: "聆听中",
    state: "send",
  },
  interrupt: {
    text: "思考中... \n (请等待大约3至4秒时间)",
    state: "interrupt",
  },
};
const stateHandler = (state1) => {
  let start = stateText[state1];
  stateBtn.value = start.state;
  state.value = start.text;
};

// 处理语音消息
async function handleVoice(text) {
  // transcript.value = "请求中..."
  // console.log("Final Transcript: " + transcript); // 输出最终结果
  if (stateBtn.value !== "interrupt") return;
  transcript.value = "";
  let res = await chatMessage({
    text,
    conversation_id,
    isAudio: true,
  });
  console.log("res---", res);
  if (stateBtn.value !== "interrupt") return;
  state.value = "";
  // conversation_id = res.data.conversation_id
  let answer = res.data.answer;
  responseContent.value = JSON.stringify(answer);
  // 播放语音
  playVoice(res.data.url);
  playText(answer);
}
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function playText(answer) {
  (async () => {
    let groups = splitIntoGroups(answer, 15);
    for (const group of groups) {
      if (stateBtn.value !== "interrupt") break;
      transcript.value = group;
      await sleep(3100);
    }
  })();
}
// 分割文字
function splitIntoGroups(str, groupSize) {
  let result = [];
  for (let i = 0; i < str.length; i += groupSize) {
    result.push(str.slice(i, i + groupSize));
  }
  return result;
}
function speechStart() {
  audioSpeechStar(transcript, async function () {
    console.log("发送网络请求");
    let answer = transcript.value;
    isResponse = true;
    try {
      // 识别结束 发送网络请求
      await handleVoice(answer);
    } catch (e) {}
    isResponse = false;
  });
}
function startListening() {
  // 播放语音
  stateHandler("send");
  try {
    let audio = audioElement.value;
    audio.muted = false;
    voicePath.value = "";
    audio.volume = 1;
    audio.load();
    audio.play();
    nextTick(() => {
      audio.pause();
      speechStart();
    });
  } catch (e) {}
}
onMounted(() => {
  // dblclick 是 JavaScript 中用于监听双击（double click）事件的事件类型。当用户在同一元素上快速连续点击两次时，会触发 dblclick 事件。
  document.addEventListener(
    "dblclick",
    function (e) {
      e.preventDefault();
    },
    { passive: false }
  );
  fetchSystemSettings();
});

function stopListening() {
  stateHandler("interrupt");
  audioSpeechStop();
}
function playStop() {
  const audio = audioElement.value;
  audio.muted = true;
  audio.pause();
}
function cancelState() {
  playStop();
  audioSpeechStop();
  transcript.value = "";
  stateHandler("start");
}
const audioElement = ref(null);
const voicePath = ref("");
const playVoice = (url) => {
  console.log("audioElement--", audioElement);
  // 外部链接
  // voicePath.value = `https://voice-1329755876.cos.ap-guangzhou.myqcloud.com/temp-0f82ada4-7ca3-42d9-9d6f-394df0ae5eb0.mp3`
  try {
    voicePath.value = url;
    const audio = audioElement.value;
    // 从头开始
    audio.currentTime = 0;
    audio.muted = false;
    // 重新加载
    audio.load();
    nextTick(() => {
      // 播放
      audio?.play();
    });
  } catch (e) {}
};
const audioEndHandle = () => {
  if (isResponse) {
    transcript.value = "思考中...";
    return;
  }
  flag = false;
  const audio = audioElement.value;
  audio.muted = true;
  audio.pause();
  // statusText.value = "请说话.."
  transcript.value = "请说话..";
  speechStart();
  stateHandler("send");
};
// 打断
const interruptClick = () => {
  playStop();
  speechStart();
  stateHandler("send");
};

const backgroundImage = ref("/images/home/<USER>");
// 根据后端信息更新的背景图片地址
// 获取系统设置
const fetchSystemSettings = async () => {
  const settings = await getSystemSettings();
  if (settings) {
    backgroundImage.value = settings.background; //  更新存储背景图 URL
  }
};
</script>

<template>
  <audio
    :src="voicePath"
    @ended="audioEndHandle()"
    ref="audioElement"
    controls
    style="display: none"
  ></audio>

  <div class="app">
    <div
      class="center-gif"
      :style="{ backgroundImage: `url(${backgroundImage})` }"
    ></div>
    <div class="content">
      <div class="icon">
        <router-link to="/chat">
          <img class="img" src="@/assets/text.png" alt="" />
        </router-link>
      </div>
      <div class="text-tip">文字互动</div>

      <div class="text-out-box">
        <div class="state-box" v-show="transcript === ''">
          <div>{{ state }}</div>
        </div>
        <div class="cartoon" v-show="stateBtn === 'send'">
          <img src="/images/home/<USER>" alt="" />
        </div>
        <div class="text-box">{{ transcript }}</div>
      </div>
      <!--      <button class="button" @click="clickListening()">-->
      <!--        {{btnText}}-->
      <!--      </button>-->
      <div class="submit">
        <div v-show="stateBtn === 'start'" class="action-group">
          <img
            class="start"
            @click="startListening()"
            :src="`/images/home/<USER>"
            alt=""
          />
          <div class="sendtext">对话</div>
        </div>
        <div v-show="stateBtn === 'send'" class="action-group">
          <img
            class="send"
            @click="stopListening()"
            :src="`/images/home/<USER>"
            alt=""
            style="border-radius: 50%; "
          />
          <div class="sendtext">发送</div>
        </div>
        <div v-show="stateBtn === 'interrupt'" class="action-group">
          <img
            class="interrupt"
            @click="interruptClick()"
            src="/images/home/<USER>"
            alt=""
          />
          <div class="sendtext">打断</div>
        </div>
      </div>
      <div class="cancel">
        <img
          @click="cancelState()"
          src="/images/home/<USER>"
          alt="终止沟通"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
body,
html,
.app {
  background-color: rgb(247, 247, 245);
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 添加文本容器样式 */
.text-container {
  background-color: rgba(255, 255, 255, 0.7); /* 半透明白色背景 */
  backdrop-filter: blur(8px); /* 模糊效果 */
  border-radius: 16px; /* 圆角 */
  padding: 20px; /* 内边距 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 阴影增加层次感 */
  max-width: 80%; /* 最大宽度 */
}

.content {
  text-align: center;
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  color: #000;
  font-weight: bold;
  .text-out-box {
    position: absolute;
    left: 50%;
    width: 80%;
    transform: translateX(-50%);
    bottom: 13rem;
    font-size: 16px;
    backdrop-filter: blur(1px);
    .text-box {
      background-color: #e0e0e0;
      color: #000;
      padding: 10px 16px;
      border-radius: 12px;
      font-size: 43px;
      line-height: 1.5;
      //当文字框内无信息时为透明
      &:empty {
        background-color: transparent;
        padding: 0;
      }
    }
  }
  .cartoon {
    position: absolute;
    top: -510px;  // 距离顶部的距离
    left: 50%;
    transform: translateX(-50%);
    z-index: 10; // 确保浮在中间内容上方

    img {
      width: 50px;
      height: 70px;
    }
  }

  .state-box {
    color: #000; /* 字体颜色改为黑色 */
    background-color: rgba(220, 220, 220, 0.7); // 更明显的灰色
    backdrop-filter: blur(8px); /* 模糊效果 */
    border-radius: 12px; /* 圆角 */
    padding: 6px; /* 内边距 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 阴影增加层次感 */
    white-space: pre-wrap;
    font-size: 36px;
    width: fit-content; //根据内容自适应宽度
    max-width: 90%; //限制最长宽度，防止撑满屏
    margin: 0 auto;
  }
  .submit {
    position: absolute;
    width: 80%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 5rem;
    display: flex; //  横向排列
    justify-content: center;
    gap: 20px;
    img {
      width: 80px;
      height: 80px;
    }
  }
  .action-group {
    display: flex;
    flex-direction: column; //  竖直排列图标和文字
    align-items: center;
  }

  .cancel {
    position: absolute;
    width: 80%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0.5rem;
    img {
      width: 40px;
      height: 40px;
      border: 1px solid #fff;
    }
  }
}

.icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon .img {
  width: 60px;
  height: 60px;
}
.text-tip {
  position: absolute;
  top: 75px;
  right: 5px;
  color: #000; /* 字体颜色改为黑色 */
  // font-weight: bold; //字体加粗
  transform: translateX(-14px); // ✅ 左移 px 对齐图标
}
.icon-dots {
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='20' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='50' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='80' cy='20' r='5' fill='%2300008B'/%3E%3Ccircle cx='35' cy='50' r='5' fill='%2300008B'/%3E%3Ccircle cx='65' cy='50' r='5' fill='%2300008B'/%3E%3Ccircle cx='50' cy='80' r='5' fill='%2300008B'/%3E%3C/svg%3E");
  background-size: cover;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
}

.light-spot {
  position: absolute;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(63, 255, 255, 0.9) 0%,
    rgba(63, 255, 255, 0) 70%
  );
  border-radius: 50%;
  top: 50%;
  left: 50%;
  animation: pulse 2s infinite;
  z-index: 0;
}
.button {
  background: linear-gradient(
    to right,
    rgba(135, 206, 250, 0.8),
    rgba(30, 144, 255, 0.8)
  );
  color: white;
  border: none;
  padding: 12px 40px;
  border-radius: 30px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
  position: absolute;
  width: 80%;
  left: 50%;
  transform: translateX(-50%);
  bottom: 2rem;
}

.button:hover {
  background: linear-gradient(
    to right,
    rgba(30, 144, 255, 0.8),
    rgba(135, 206, 250, 0.8)
  );
}

.center-gif {
  position: absolute;
  top: 50%;
  left: 50%;
  width: calc(100% - 80px); // 两侧保留 40px 间距
  max-width: 580px;
  height: 600px;
  transform: translate(-50%, -50%);
  background-image: url("/images/home/<USER>"); // 默认图（可被动态替换）
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 0;
  opacity: 0.7;
  pointer-events: none;
}
</style>
<style lang="scss">
.bg-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-image: url("/images/home/<USER>");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

}
.sendtext {
  margin-top: 10px; /* 把按钮向上移动10px */
  color: #000; /* 黑色字体 */
  font-weight: bold;
  background-color: rgba(220, 220, 220, 0.7); /* 半透明白色背景 */
  backdrop-filter: blur(2px); /* 模糊效果 */
  border-radius: 12px; /* 圆角 */
  padding: 3px 10px; /* 内边距 */
  display: inline-block; /* 使背景仅包裹文字 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
}
</style>
