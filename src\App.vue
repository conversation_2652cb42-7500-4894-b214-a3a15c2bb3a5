<script setup>
import {onMounted, ref} from 'vue'
import {detectDevice,isWeChatBrowser} from '@/utils/commonUtils.js'

const device = ref(detectDevice());
const wexin = ref(isWeChatBrowser())
const mainElement = ref(false)
onMounted(()=>{
  if(wexin.value){
    mainElement.value.style = "height: 100vh"
  }else if (device.value === "iOS"){
    mainElement.value.style = "height: calc(100vh - 74px)"
  }
})
</script>

<template>
  <div class="main" ref="mainElement">
    <router-view/> <!-- 路由出口 -->
  </div>
</template>

<style scoped>
.main {
  height: calc(100vh);
}

</style>
