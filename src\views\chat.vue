<script setup>
import { useRouter } from 'vue-router';
import {ref, onMounted, onUnmounted, nextTick} from "vue";
import {chatMessage, voice2Text} from "@/http/voiceApi.js";
import {getTimeString} from '@/utils/date.js'
import {audioSpeechStar, audioSpeechStop} from "@/utils/audioUtils.js"
import {getStartVoice, getResponseVoice} from "@/utils/commonUtils.js";
import {
  Toast
} from 'antd-mobile'
const router = useRouter(); // 使用useRouter函数获取路由实例
const message = ref('');
const chatContainer = ref(null);
// 蒙版是否显示
const maskShow = ref(false);
// 按钮状态
const btnState = ref(false);
// 提示状态
const tipState = ref(false);

// 聊天消息数组
const messageArray = ref([]);
onMounted(()=>{
  let item = getStartVoice()
  let msg = {
    message:item.text,
    type: 'system',
    date:'10:30 AM'
  }
  messageArray.value.push(msg)
})
const sendMessage = async ()=>{
  const text = message.value;
  if (!text){
    Toast.show("文字不能为空");
    return;
  }
  message.value = ''
  // 添加数组
  let user = {
    message: text,
    type:'user',
    date: getTimeString()
  }
  messageArray.value.push(user);
  // 系统回复
  // systemInsert()
  await scrollView()
  tipState.value = true
  let res = await chatMessage({
    text,
  })
  tipState.value = false
  if (res.code === 200){
    let data = res.data;
    let system = {
      message: data.answer,
      type:'system',
      date: getTimeString()
    }
    messageArray.value.push(system);
  }
  console.log(res);
  await scrollView()
}
// 系统回答
const systemInsert = ()=>{
  setTimeout(async ()=>{
    let item = getResponseVoice()
    // 随机响应
    let system1 = {
      message: item.text,
      type:'system',
      date: getTimeString()
    }
    messageArray.value.push(system1);
    await scrollView()
  },1000)
}
// 调整页面滚动到底部
const scrollView = async ()=> {
  await nextTick(()=>{
    let height = chatContainer.value.scrollHeight;
    console.log(height)
    // console.log(chatContainer.value)
    chatContainer.value.scroll(0,height)
  })
}
// 切换按钮
const handleSwitch = () => {
  btnState.value = !btnState.value;
}

let resultText = ref("");
// 按下录音
const handleMouseDown = async (e) => {
  maskShow.value = true
  audioSpeechStar(resultText)
};
// 松开发送录音
const handleMouseUp = () => {
  maskShow.value = false
  console.log('按钮抬起，结束录音');
  // 音频停止
  audioSpeechStop()
  // 发送消息
  message.value = resultText.value;
  sendMessage()
};
const handleCancel = () => {
  maskShow.value = false
  resultText.value = '';
}
</script>

<template>
  <div class="chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="chat-title">
        <h4>Terry's AI</h4>
        <img src="@/assets/down.png" alt="">
      </div>
      <router-link to="/">
        <div class="chat-menu">
          <img src="@/assets/chat.png" alt="">
          <div class="tip">语音互动</div>
        </div>
      </router-link>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" id="chatMessages" ref="chatContainer">
      <!-- 示例消息 -->
      <div class="message" :class="item.type==='system'?'received':'sent'" v-for="(item,index) in messageArray">
        <div class="message-content">
          <div class="message-text">{{item.message}}</div>
<!--          <div class="message-time">{{item.date}}</div>-->
        </div>
      </div>
    </div>
<!--    提示状态 -->
    <div class="tip-box" v-show="tipState">思考中...</div>
    <!-- 输入区域 -->
    <div class="chat-input-area">
      <div class="message-input">
        <input v-if="!btnState" type="text" v-model="message" id="messageInput"  placeholder="在这输入文字..." />
        <div v-if="btnState" @click="handleMouseDown()" class="voice">按下说话</div>
      </div>
      <div class="input-addons" @click="handleSwitch(e)">
        <img src="@/assets/voice.png" alt="">
      </div>
      <button class="btn-send" id="sendButton" @click="sendMessage()">
        <img src="@/assets/send.png" alt="">
      </button>
    </div>
    <div v-show="maskShow" class="mask">
      <div class="content" style="color: white">{{resultText}}</div>
      <img class="msg" src="@/assets/message.png" alt="">
      <div class="btn-box">
        <div class="btn btn-submit" @click="handleMouseUp()">发送文字</div>
        <div class="btn btn-cancel" @click="handleCancel()">取消</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.voice{
  text-align: center;
}
.tip-box{
  color: gray;
  text-indent: 20px;
}
.mask{
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .7);
  .msg{
    width: 60px;
    height: 30px;
    color: #94eb69;
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -40%);
  }
  .btn-box{
    display: flex;
    position: absolute;
    left: 50%;
    transform: translate(-50%,0);
    bottom: 80px;
    .btn{
      background-color: #fff;
      width: 130px;
      height: 50px;
      border-radius: 10px;
      text-align: center;
      line-height: 50px;
      font-size: 16px;
    }
    .btn-submit{
      background-color: #94eb69;
    }
    .btn-cancel{
      margin-left: 30px;
    }
  }
  .content{
    width: 80%;
    position: absolute;
    left: 50%;
    transform: translate(-50%,0);
    bottom: 170px;
    font-size: 16px;
  }
}
/* 聊天容器 */
.chat-container {
  width: 100%;
  //max-width: 500px;
  //height: calc(100vh - 74px);
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  //background-color: #007bff;
  background-color: #fff;
  color: white;
  //box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-btn, .chat-menu {
  font-size: 20px;
  cursor: pointer;
}
.chat-menu img{
  width: 50px;
  height: 50px;
}
.chat-menu .tip{
  font-size: 14px;
  color:black;
}
.back-btn img{
  width: 20px;
  height: 20px;
}
.chat-title{
  display: flex;
}
.chat-title h4 {
  margin: 0;
  font-size: 18px;
  color: black;
}
.chat-title img{
  width: 10px;
  height: 10px;
  margin-top: 5px;
  margin-left: 5px;
}
/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  //background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  color: black;
}

.message {
  margin-bottom: 15px;
  max-width: 80%;
  display: flex;
}

.message.received {
  align-self: flex-start;
}
.received .message-text{
  background-color:  #f9f9f9 !important;
}
.message.sent {
  align-self: flex-end;
}

.message-content {
  display: flex;
  flex-direction: column;
}

.message-text {
  padding: 10px 15px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.received .message-text {
  background-color: white;
  //border-top-left-radius: 4px;
}

.message.sent .message-text {
  //background-color: #007bff;
  color: white;
  background-color: #2353A6;
  //border-top-right-radius: 4px;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  align-self: flex-end;
}

/* 输入区域 */
.chat-input-area {
  display: flex;
  align-items: center;
  //padding: 10px;
  height: 50px;
  width: 92%;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 80px;
  margin: 0 auto;
  margin-bottom: 20px;
  margin-top: 20px;
}

.input-addons {
  display: flex;
  gap: 10px;
}
.input-addons img{
  width: 25px;
  height: 25px;
}
.btn-addon {
  background: none;
  border: none;
  color: #666;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
}

.message-input {
  flex: 1;
}

.message-input input, .voice {
  width: 100%;
  padding: 10px 15px;
  border-radius: 20px;
  //border: 1px solid #eee;
  border: none;
  outline: none;
  font-size: 15px;
}

.btn-send {
  //background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 10px;
}
.btn-send img{
  width: 35px;
  height: 35px;
}
</style>