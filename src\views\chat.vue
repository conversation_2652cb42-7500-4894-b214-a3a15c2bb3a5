<script setup>
import { useRouter } from "vue-router";
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { chatMessage, voice2Text } from "@/http/voiceApi.js";
import { getTimeString } from "@/utils/date.js";
import { audioSpeechStar, audioSpeechStop } from "@/utils/audioUtils.js";
import { getStartVoice, getResponseVoice } from "@/utils/commonUtils.js";
import { Toast } from "antd-mobile";
import { getSystemSettings } from "@/http/settingApi";//获取头像标题，背景。
const router = useRouter(); // 使用useRouter函数获取路由实例
const message = ref("");
const chatContainer = ref(null);
// 蒙版是否显示
const maskShow = ref(false);
// 按钮状态
const btnState = ref(false);
// 提示状态
const tipState = ref(false);

const pageTitle = ref("<PERSON>'s Coaching AI"); // 默认标题
const defaultAvatar = ref('/images/AIprofile/ai-avatar.png'); // 默认头像
const backgroundImage = ref("/images/home/<USER>"); // 根据后端信息更新的背景图片地址

// 聊天消息数组
const messageArray = ref([]);

// 新增：获取系统设置
const fetchSystemSettings = async () => {
  const settings = await getSystemSettings();
  if (settings) {
    pageTitle.value = settings.title; // 更新标题
    defaultAvatar.value = settings.avatar; // 更新默认头像路径
    backgroundImage.value = settings.background; //  更新存储背景图 URL
  }
};

onMounted(async () => {
  await fetchSystemSettings(); // 加载时获取设置

  let item = getStartVoice();
  let msg = {
    message: item.text,
    type: "system",
    date: "10:30 AM",
    avatar: defaultAvatar.value // 使用动态头像
  };
  messageArray.value.push(msg);
});

const sendMessage = async () => {
  const text = message.value;
  if (!text) {
    Toast.show("文字不能为空");
    return;
  }
  message.value = "";
  // 添加数组
  let user = {
    message: text,
    type: "user",
    date: getTimeString(),
  };
  messageArray.value.push(user);
  // 系统回复
  // systemInsert()
  await scrollView();
  tipState.value = true;
  let res = await chatMessage({
    text,
  });
  tipState.value = false;
  if (res.code === 200) {
    let data = res.data;
    let system = {
      message: data.answer,
      type: "system",
      date: getTimeString(),
      avatar: defaultAvatar.value // 使用动态头像
    };
    messageArray.value.push(system);
  }
  console.log(res);
  await scrollView();
};
// 系统回答
const systemInsert = () => {
  setTimeout(async () => {
    let item = getResponseVoice();
    // 随机响应
    let system1 = {
      message: item.text,
      type: "system",
      date: getTimeString(),
      //之后导入头像信息
    };
    messageArray.value.push(system1);
    await scrollView();
  }, 1000);
};
// 调整页面滚动到底部
const scrollView = async () => {
  await nextTick(() => {
    let height = chatContainer.value.scrollHeight;
    console.log(height);
    // console.log(chatContainer.value)
    chatContainer.value.scroll(0, height);
  });
};
// 切换按钮
const handleSwitch = () => {
  btnState.value = !btnState.value;
};

let resultText = ref("");
// 按下录音
const handleMouseDown = async (e) => {
  maskShow.value = true;
  audioSpeechStar(resultText);
};
// 松开发送录音
const handleMouseUp = () => {
  maskShow.value = false;
  console.log("按钮抬起，结束录音");
  // 音频停止
  audioSpeechStop();
  // 发送消息
  message.value = resultText.value;
  sendMessage();
};
const handleCancel = () => {
  maskShow.value = false;
  resultText.value = "";
};
</script>

<template>
  <div class="chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="chat-title">
        <h4 style="font-size: 24px; font-weight: bold;">{{ pageTitle }}</h4>
        <img src="@/assets/down.png" alt="下拉菜单" style="margin-top: 15px;"/>
      </div>
      <router-link to="/">
        <div class="chat-menu">
          <img src="@/assets/chat.png" alt="" />
          <div class="tip">语音互动</div>
        </div>
      </router-link>
    </div>
    <div class="center-gif" :style="{ backgroundImage: `url(${backgroundImage})` }"></div>

    <!-- 空白占位 -->
    <div class="chat-space-placeholder"></div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" id="chatMessages" ref="chatContainer">
      <div
        class="message"
        :class="item.type === 'system' ? 'received' : 'sent'"
        v-for="(item, index) in messageArray"
        :key="index"
      >
        <!-- AI 回复，显示头像 -->
        <template v-if="item.type === 'system'">
          <div class="ai-avatar">
            <img :src="item.avatar || defaultAvatar" alt="AI Avatar" />
          </div>
          <div class="message-content">
            <div class="message-text">{{ item.message }}</div>
          </div>
        </template>

        <!-- 用户消息 -->
        <template v-else>
          <div class="message-content">
            <div class="message-text">{{ item.message }}</div>
          </div>
        </template>
      </div>

      <!-- 思考中提示 -->
      <div class="tip-box" v-show="tipState">思考中...</div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-area">
      <div class="message-input">
        <input
          v-if="!btnState"
          type="text"
          v-model="message"
          id="messageInput"
          placeholder="在这输入文字..."
        />
        <div v-if="btnState" @click="handleMouseDown()" class="voice">
          按下说话
        </div>
      </div>
      <div class="input-addons" @click="handleSwitch">
        <img src="@/assets/voice.png" alt="" />
      </div>
      <button class="btn-send" id="sendButton" @click="sendMessage()">
        <img src="@/assets/send.png" alt="" />
      </button>
    </div>

    <!-- 语音遮罩 -->
    <div v-show="maskShow" class="mask">
      <div class="content" style="color: white">{{ resultText }}</div>
      <img class="msg" src="@/assets/message.png" alt="" />
      <div class="btn-box">
        <div class="btn btn-submit" @click="handleMouseUp()">发送文字</div>
        <div class="btn btn-cancel" @click="handleCancel()">取消</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.voice {
  text-align: center;
}
.tip-box {
  color: gray;
  text-indent: 20px;
}
.mask {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  .msg {
    width: 60px;
    height: 30px;
    color: #94eb69;
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -40%);
  }
  .btn-box {
    display: flex;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    bottom: 80px;
    .btn {
      background-color: #fff;
      width: 130px;
      height: 50px;
      border-radius: 10px;
      text-align: center;
      line-height: 50px;
      font-size: 16px;
    }
    .btn-submit {
      background-color: #94eb69;
    }
    .btn-cancel {
      margin-left: 30px;
    }
  }
  .content {
    width: 80%;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    bottom: 170px;
    font-size: 16px;
  }
}
/* 聊天容器 */
.chat-container {
  width: 100%;
  //max-width: 500px;
  //height: calc(100vh - 74px);
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgb(247,247,245);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  //background-color: #007bff;
  background-color: rgb(247,247,245);
  color: white;
  //box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-btn,
.chat-menu {
  font-size: 20px;
  cursor: pointer;
}
.chat-menu img {
  width: 50px;
  height: 50px;
}
.chat-menu .tip {
  font-size: 12px;
  color: black;
  font-weight: bold;
}
.back-btn img {
  width: 20px;
  height: 20px;
}
.chat-title {
  display: flex;
}
.chat-title h4 {
  margin: 0;
  font-size: 18px;
  color: black;
}
.chat-title img {
  width: 10px;
  height: 10px;
  margin-top: 5px;
  margin-left: 5px;
}

.chat-space-placeholder {
  height: 40%;
  min-height: 100px; //  一个占位div将消息区域向下挤压
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  //background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  color: black;
  max-height: 55%; // 限制高度为剩下的一半
  overflow-y: auto;
}

.message {
  margin-bottom: 15px;
  max-width: 80%;
  display: flex;
  align-items: flex-start; 
}

.message.received {
  align-self: flex-start;
  flex-direction: row;//头像左信息右
}
// .received .message-text {
//   background-color: #f9f9f9 !important;
// }
.message.sent {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-content {
  display: flex;
  flex-direction: column;
}

.message-text {
  padding: 10px 15px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.received .message-text {
  background-color: rgba(255, 255, 255, 0.8);
  //border-top-left-radius: 4px;
}

.message.sent .message-text {
  //background-color: #007bff;
  color: white;
  background-color: rgba(35, 83, 166, 0.8);
  //border-top-right-radius: 4px;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  align-self: flex-end;
}

/* 输入区域 */
.chat-input-area {
  display: flex;
  align-items: center;
  //padding: 10px;
  height: 50px;
  width: 92%;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 80px;
  margin: 0 auto;
  margin-bottom: 20px;
  margin-top: 20px;
}

.input-addons {
  display: flex;
  gap: 10px;
}
.input-addons img {
  width: 25px;
  height: 25px;
}
.btn-addon {
  background: none;
  border: none;
  color: #666;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
}

.message-input {
  flex: 1;
}

.message-input input,
.voice {
  width: 100%;
  padding: 10px 15px;
  border-radius: 20px;
  //border: 1px solid #eee;
  border: none;
  outline: none;
  font-size: 15px;
}

.btn-send {
  //background-color: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 10px;
}
.btn-send img {
  width: 35px;
  height: 35px;
}
/* AI头像样式 */
.ai-avatar {
  width: 50px; // 头像宽度
  height: 50px; // 头像高度
  border-radius: 50%; // 圆形头像
  overflow: hidden; // 超出部分隐藏
  margin-right: 10px; // 头像与消息之间的间距
  flex-shrink: 0;    // 防止在小屏幕上被压缩
}

.ai-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover; // 图片填充方式
}
// 背景gif
.center-gif {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 600px; // 可按需调整
  height: 600px;
  max-width: calc(100% - 80px); // 左右各保留20px边距
  transform: translate(-50%, -50%);
  background-image: url('/images/home/<USER>'); // gif背景图的路径
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 0; // 保证在最底层，不遮住内容
  opacity: 0.7; // 可调透明度
  pointer-events: none; // 防止干扰鼠标交互
}
// 设置置于层级一
.chat-header,
.chat-messages,
.chat-input-area {
  position: relative;   //  开启堆叠上下文
  z-index: 1;            //  把它们放在 GIF 之上
}

</style>
