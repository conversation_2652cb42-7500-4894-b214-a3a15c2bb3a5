import { createRouter, createWebHistory } from 'vue-router';
import Home from '@/views/Home.vue';
import Chat from "@/views/chat.vue"
import Test from '@/views/test.vue'

const routes = [
    {
        path: '/',
        name: 'Home',
        component: Home
    },
    {
        path: '/chat',
        name: 'Chat',
        component: Chat
    }
];
const router = createRouter({
    history: createWebHistory(""),
    routes
});

export default router;